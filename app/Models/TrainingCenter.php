<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class TrainingCenter extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'location',
        'address',
        'capacity',
        'facilities',
        'is_active'
    ];

    protected $casts = [
        'facilities' => 'array',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function trainingSessions()
    {
        return $this->hasMany(TrainingSession::class);
    }

    public function activeTrainers()
    {
        return $this->hasManyThrough(
            User::class,
            TrainingSession::class,
            'training_center_id',
            'id',
            'id',
            'trainer_id'
        )->where('users.role', 'trainer')
         ->distinct();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
