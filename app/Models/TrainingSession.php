<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\TrainingCenter;
use App\Models\User;

class TrainingSession extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'training_center_id',
        'trainer_id',
        'title',
        'description',
        'start_date',
        'end_date',
        'max_participants',
        'status',
        'materials',
        'fee'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'materials' => 'array',
        'fee' => 'decimal:2'
    ];

    // Relationships
    public function trainingCenter()
    {
        return $this->belongsTo(TrainingCenter::class);
    }

    public function trainer()
    {
        return $this->belongsTo(User::class, 'trainer_id');
    }

    public function students()
    {
        return $this->belongsToMany(User::class, 'session_enrollments')
            ->withTimestamps()
            ->withPivot(['status', 'payment_status']);
    }

    // Scopes
    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', now())
                    ->where('status', 'scheduled');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Methods
    public function isEnrollmentOpen()
    {
        return $this->status === 'scheduled' && 
               $this->start_date->isFuture() && 
               $this->students()->count() < $this->max_participants;
    }
}
