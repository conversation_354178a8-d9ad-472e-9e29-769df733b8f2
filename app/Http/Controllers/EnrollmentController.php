<?php

namespace App\Http\Controllers;

use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EnrollmentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:student')->only(['store']);
        $this->middleware('role:trainer')->only(['updateStatus']);
    }

    public function index()
    {
        $user = auth()->user();
        
        if ($user->isStudent()) {
            $enrollments = $user->enrolledSessions()
                ->with(['trainingCenter', 'trainer'])
                ->withPivot(['status', 'payment_status', 'amount_paid'])
                ->paginate(10);
        } else {
            $enrollments = TrainingSession::where('trainer_id', $user->id)
                ->with(['trainingCenter', 'students'])
                ->paginate(10);
        }

        return Inertia::render('Enrollments/Index', [
            'enrollments' => $enrollments
        ]);
    }

    public function store(Request $request, TrainingSession $trainingSession)
    {
        if (!$trainingSession->isEnrollmentOpen()) {
            return back()->with('error', 'Enrollment is not open for this session.');
        }

        $request->validate([
            'payment_amount' => 'required|numeric|min:0|max:' . $trainingSession->fee
        ]);

        $paymentStatus = $request->payment_amount >= $trainingSession->fee ? 'completed' : 'partial';

        $trainingSession->students()->attach(auth()->id(), [
            'status' => 'enrolled',
            'payment_status' => $paymentStatus,
            'amount_paid' => $request->payment_amount
        ]);

        return redirect()
            ->route('enrollments.index')
            ->with('success', 'Successfully enrolled in the training session.');
    }

    public function updateStatus(Request $request, TrainingSession $trainingSession)
    {
        $request->validate([
            'student_id' => 'required|exists:users,id',
            'status' => 'required|in:enrolled,in_progress,completed,dropped'
        ]);

        $trainingSession->students()->updateExistingPivot($request->student_id, [
            'status' => $request->status
        ]);

        return back()->with('success', 'Student status updated successfully.');
    }

    public function destroy(TrainingSession $trainingSession)
    {
        $user = auth()->user();
        
        if (!$user->isStudent()) {
            return back()->with('error', 'Only students can withdraw from sessions.');
        }

        $enrollment = $trainingSession->students()->where('user_id', $user->id)->first();
        
        if (!$enrollment) {
            return back()->with('error', 'You are not enrolled in this session.');
        }

        if ($enrollment->pivot->status === 'completed') {
            return back()->with('error', 'Cannot withdraw from completed sessions.');
        }

        $trainingSession->students()->detach($user->id);

        return redirect()
            ->route('enrollments.index')
            ->with('success', 'Successfully withdrawn from the training session.');
    }
}
