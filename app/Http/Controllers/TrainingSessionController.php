<?php

namespace App\Http\Controllers;

use App\Models\TrainingSession;
use App\Models\TrainingCenter;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TrainingSessionController extends Controller
{


    public function index()
    {
        $query = TrainingSession::with(['trainingCenter', 'trainer'])
            ->withCount('students');

        if (auth()->user()->isTrainer()) {
            $query->where('trainer_id', auth()->id());
        }

        $sessions = $query->latest()->paginate(10);

        return Inertia::render('TrainingSessions/Index', [
            'sessions' => $sessions
        ]);
    }

    public function create()
    {
        $centers = TrainingCenter::active()->get();
        $trainers = User::where('role', 'trainer')
            ->where('is_active', true)
            ->get();

        return Inertia::render('TrainingSessions/Create', [
            'centers' => $centers,
            'trainers' => $trainers
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'training_center_id' => 'required|exists:training_centers,id',
            'trainer_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'max_participants' => 'required|integer|min:1',
            'materials' => 'nullable|array',
            'fee' => 'required|numeric|min:0'
        ]);

        $session = TrainingSession::create($validated);

        return redirect()
            ->route('training-sessions.show', $session)
            ->with('success', 'Training session created successfully.');
    }

    public function show(TrainingSession $trainingSession)
    {
        $trainingSession->load([
            'trainingCenter',
            'trainer',
            'students' => function($query) {
                $query->withPivot(['status', 'payment_status', 'amount_paid']);
            }
        ]);

        return Inertia::render('TrainingSessions/Show', [
            'session' => $trainingSession,
            'canEnroll' => auth()->user()->isStudent() && $trainingSession->isEnrollmentOpen()
        ]);
    }

    public function edit(TrainingSession $trainingSession)
    {
        $this->authorize('update', $trainingSession);

        $centers = TrainingCenter::active()->get();
        $trainers = User::where('role', 'trainer')
            ->where('is_active', true)
            ->get();

        return Inertia::render('TrainingSessions/Edit', [
            'session' => $trainingSession,
            'centers' => $centers,
            'trainers' => $trainers
        ]);
    }

    public function update(Request $request, TrainingSession $trainingSession)
    {
        $this->authorize('update', $trainingSession);

        $validated = $request->validate([
            'training_center_id' => 'required|exists:training_centers,id',
            'trainer_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'max_participants' => 'required|integer|min:1',
            'materials' => 'nullable|array',
            'fee' => 'required|numeric|min:0',
            'status' => 'required|in:scheduled,in_progress,completed,cancelled'
        ]);

        $trainingSession->update($validated);

        return redirect()
            ->route('training-sessions.show', $trainingSession)
            ->with('success', 'Training session updated successfully.');
    }

    public function destroy(TrainingSession $trainingSession)
    {
        $this->authorize('delete', $trainingSession);

        if ($trainingSession->students()->exists()) {
            return back()->with('error', 'Cannot delete session with enrolled students.');
        }

        $trainingSession->delete();

        return redirect()
            ->route('training-sessions.index')
            ->with('success', 'Training session deleted successfully.');
    }
}
