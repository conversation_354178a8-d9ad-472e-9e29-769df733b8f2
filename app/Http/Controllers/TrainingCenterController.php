<?php

namespace App\Http\Controllers;

use App\Models\TrainingCenter;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TrainingCenterController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $centers = TrainingCenter::with(['trainingSessions' => function($query) {
            $query->upcoming();
        }])
        ->withCount('trainingSessions')
        ->paginate(10);

        return Inertia::render('TrainingCenters/Index', [
            'centers' => $centers
        ]);
    }

    public function welcome()
    {
        $trainingCenters = TrainingCenter::with('trainingSessions')->get();
        return Inertia::render('Welcome', [
            'trainingCenters' => $trainingCenters,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('TrainingCenters/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'location' => 'required|string|max:255',
            'address' => 'required|string',
            'capacity' => 'required|integer|min:1',
            'facilities' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        $center = TrainingCenter::create($validated);

        return redirect()
            ->route('training-centers.show', $center)
            ->with('success', 'Training center created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TrainingCenter $trainingCenter)
    {
        $trainingCenter->load([
            'trainingSessions' => function($query) {
                $query->with(['trainer', 'students'])
                    ->withCount('students');
            },
            'activeTrainers'
        ]);

        return Inertia::render('TrainingCenters/Show', [
            'center' => $trainingCenter
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TrainingCenter $trainingCenter)
    {
        return Inertia::render('TrainingCenters/Edit', [
            'center' => $trainingCenter
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TrainingCenter $trainingCenter)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'location' => 'required|string|max:255',
            'address' => 'required|string',
            'capacity' => 'required|integer|min:1',
            'facilities' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        $trainingCenter->update($validated);

        return redirect()
            ->route('training-centers.show', $trainingCenter)
            ->with('success', 'Training center updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TrainingCenter $trainingCenter)
    {
        if ($trainingCenter->trainingSessions()->where('status', '!=', 'completed')->exists()) {
            return back()->with('error', 'Cannot delete center with active training sessions.');
        }

        $trainingCenter->delete();

        return redirect()
            ->route('training-centers.index')
            ->with('success', 'Training center deleted successfully.');
    }
}
