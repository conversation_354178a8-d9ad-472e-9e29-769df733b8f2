<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TrainingCenter;
use App\Models\TrainingSession;
use App\Models\User;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        switch ($user->role) {
            case 'admin':
                return $this->adminDashboard();
            case 'trainer':
                return $this->trainerDashboard();
            case 'student':
                return $this->studentDashboard();
            case 'finance':
                return $this->financeDashboard();
            default:
                return redirect()->route('login');
        }
    }

    private function adminDashboard()
    {
        $data = [
            'centers_count' => TrainingCenter::count(),
            'active_trainers' => User::where('role', 'trainer')->where('is_active', true)->count(),
            'total_students' => User::where('role', 'student')->count(),
            'upcoming_sessions' => TrainingSession::upcoming()->count(),
            'recent_centers' => TrainingCenter::latest()->take(5)->get(),
            'recent_sessions' => TrainingSession::with('trainingCenter', 'trainer')
                ->latest()
                ->take(5)
                ->get()
        ];

        return Inertia::render('Dashboard/Admin', $data);
    }

    private function trainerDashboard()
    {
        $trainer = auth()->user();
        
        $data = [
            'active_sessions' => $trainer->trainingSessions()
                ->where('status', 'in_progress')
                ->with('trainingCenter', 'students')
                ->get(),
            'upcoming_sessions' => $trainer->trainingSessions()
                ->upcoming()
                ->with('trainingCenter')
                ->get(),
            'completed_sessions' => $trainer->trainingSessions()
                ->where('status', 'completed')
                ->count(),
            'total_students' => $trainer->trainingSessions()
                ->withCount('students')
                ->get()
                ->sum('students_count')
        ];

        return Inertia::render('Dashboard/Trainer', $data);
    }

    private function studentDashboard()
    {
        $student = auth()->user();
        
        $data = [
            'enrolled_sessions' => $student->enrolledSessions()
                ->with('trainingCenter', 'trainer')
                ->get(),
            'completed_sessions' => $student->enrolledSessions()
                ->wherePivot('status', 'completed')
                ->count(),
            'pending_payments' => $student->enrolledSessions()
                ->wherePivot('payment_status', 'pending')
                ->count(),
            'upcoming_sessions' => TrainingSession::upcoming()
                ->whereDoesntHave('students', function($query) use ($student) {
                    $query->where('users.id', $student->id);
                })
                ->with('trainingCenter', 'trainer')
                ->take(5)
                ->get()
        ];

        return Inertia::render('Dashboard/Student', $data);
    }

    private function financeDashboard()
    {
        $data = [
            'total_revenue' => \DB::table('session_enrollments')
                ->where('payment_status', 'completed')
                ->sum('amount_paid'),
            'pending_payments' => \DB::table('session_enrollments')
                ->where('payment_status', 'pending')
                ->count(),
            'partial_payments' => \DB::table('session_enrollments')
                ->where('payment_status', 'partial')
                ->count(),
            'recent_payments' => \DB::table('session_enrollments')
                ->where('payment_status', 'completed')
                ->orderBy('updated_at', 'desc')
                ->take(5)
                ->get()
        ];

        return Inertia::render('Dashboard/Finance', $data);
    }
}
