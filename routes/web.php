<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\TrainingCenterController;
use App\Http\Controllers\TrainingSessionController;
use App\Http\Controllers\EnrollmentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', [TrainingCenterController::class, 'welcome']);

Route::middleware(['auth'])->group(function () {
    // Dashboard routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Training Center routes - public access for index and show
    Route::get('/training-centers', [TrainingCenterController::class, 'index'])->name('training-centers.index');
    Route::get('/training-centers/{trainingCenter}', [TrainingCenterController::class, 'show'])->name('training-centers.show');

    // Training Center routes - admin only
    Route::middleware('role:admin')->group(function () {
        Route::get('/training-centers/create', [TrainingCenterController::class, 'create'])->name('training-centers.create');
        Route::post('/training-centers', [TrainingCenterController::class, 'store'])->name('training-centers.store');
        Route::get('/training-centers/{trainingCenter}/edit', [TrainingCenterController::class, 'edit'])->name('training-centers.edit');
        Route::put('/training-centers/{trainingCenter}', [TrainingCenterController::class, 'update'])->name('training-centers.update');
        Route::delete('/training-centers/{trainingCenter}', [TrainingCenterController::class, 'destroy'])->name('training-centers.destroy');
    });

    // Training Session routes
    Route::resource('training-sessions', TrainingSessionController::class);

    // Enrollment routes
    Route::prefix('enrollments')->name('enrollments.')->group(function () {
        Route::get('/', [EnrollmentController::class, 'index'])->name('index');
        Route::post('/sessions/{trainingSession}', [EnrollmentController::class, 'store'])
            ->middleware('role:student')
            ->name('store');
        Route::patch('/sessions/{trainingSession}/status', [EnrollmentController::class, 'updateStatus'])
            ->middleware('role:trainer')
            ->name('update-status');
        Route::delete('/sessions/{trainingSession}', [EnrollmentController::class, 'destroy'])
            ->name('destroy');
    });
});

require __DIR__.'/auth.php';
