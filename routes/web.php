<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\TrainingCenterController;
use App\Http\Controllers\TrainingSessionController;
use App\Http\Controllers\EnrollmentController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', [TrainingCenterController::class, 'welcome']);

Route::middleware(['auth'])->group(function () {
    // Dashboard routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Training Center routes
    Route::resource('training-centers', TrainingCenterController::class);

    // Training Session routes
    Route::resource('training-sessions', TrainingSessionController::class);

    // Enrollment routes
    Route::prefix('enrollments')->name('enrollments.')->group(function () {
        Route::get('/', [EnrollmentController::class, 'index'])->name('index');
        Route::post('/sessions/{trainingSession}', [EnrollmentController::class, 'store'])
            ->middleware('role:student')
            ->name('store');
        Route::patch('/sessions/{trainingSession}/status', [EnrollmentController::class, 'updateStatus'])
            ->middleware('role:trainer')
            ->name('update-status');
        Route::delete('/sessions/{trainingSession}', [EnrollmentController::class, 'destroy'])
            ->name('destroy');
    });
});

require __DIR__.'/auth.php';
