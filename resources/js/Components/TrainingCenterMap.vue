<template>
  <div class="map-container">
    <div id="map" class="w-full h-[500px]"></div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

const props = defineProps({
  trainingCenters: Array,
});

onMounted(() => {
  const map = L.map('map').setView([0.3476, 32.5825], 8); // Default to Uganda's coordinates

  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
  }).addTo(map);

  // Add markers for each training center
  props.trainingCenters.forEach(center => {
    const marker = L.marker([center.latitude, center.longitude]).addTo(map);
    marker.bindPopup(`
      <b>${center.name}</b><br>
      <b>District:</b> ${center.district}<br>
      <b>Address:</b> ${center.address}<br>
      <b>Sessions:</b> ${center.training_sessions.map(session => session.title).join(', ')}
    `);
  });
});
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
}
</style>