<template>
  <nav v-if="links.length > 3">
    <ul class="pagination">
      <li v-for="(link, index) in links" :key="index" class="page-item" :class="{ 'active': link.active, 'disabled': !link.url }">
        <a class="page-link" :href="link.url" v-html="link.label" @click.prevent="handleClick(link)"></a>
      </li>
    </ul>
  </nav>
</template>

<script setup>
defineProps({
  links: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['page-change']);

function handleClick(link) {
  if (link.url) {
    emit('page-change', link.url);
  }
}
</script>

<style scoped>
.pagination {
  @apply flex justify-center mt-4;
}

.page-item {
  @apply mx-1;
}

.page-link {
  @apply px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50;
}

.page-item.active .page-link {
  @apply bg-blue-500 text-white border-blue-500;
}

.page-item.disabled .page-link {
  @apply text-gray-400 cursor-not-allowed bg-gray-100;
}
</style>
