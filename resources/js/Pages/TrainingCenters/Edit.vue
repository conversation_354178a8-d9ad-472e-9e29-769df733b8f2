<template>
  <AppLayout :title="`Edit ${trainingCenter.name}`">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Edit Training Center: {{ trainingCenter.name }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <form @submit.prevent="submit">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Name -->
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                <input
                  v-model="form.name"
                  type="text"
                  id="name"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.name" class="text-sm text-red-600 mt-1">
                  {{ form.errors.name }}
                </p>
              </div>

              <!-- Location -->
              <div>
                <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                <input
                  v-model="form.location"
                  type="text"
                  id="location"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.location" class="text-sm text-red-600 mt-1">
                  {{ form.errors.location }}
                </p>
              </div>

              <!-- Address -->
              <div class="col-span-full">
                <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                <textarea
                  v-model="form.address"
                  id="address"
                  rows="3"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                ></textarea>
                <p v-if="form.errors.address" class="text-sm text-red-600 mt-1">
                  {{ form.errors.address }}
                </p>
              </div>

              <!-- Capacity -->
              <div>
                <label for="capacity" class="block text-sm font-medium text-gray-700">Capacity</label>
                <input
                  v-model="form.capacity"
                  type="number"
                  id="capacity"
                  min="1"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.capacity" class="text-sm text-red-600 mt-1">
                  {{ form.errors.capacity }}
                </p>
              </div>

              <!-- Facilities -->
              <div>
                <label class="block text-sm font-medium text-gray-700">Facilities</label>
                <div class="mt-1 space-y-2">
                  <div v-for="(facility, index) in form.facilities" :key="index" class="flex items-center">
                    <input
                      v-model="form.facilities[index]"
                      type="text"
                      class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                    <button
                      type="button"
                      @click="removeFacility(index)"
                      class="ml-2 text-red-600 hover:text-red-900"
                    >
                      Remove
                    </button>
                  </div>
                  <button
                    type="button"
                    @click="addFacility"
                    class="text-indigo-600 hover:text-indigo-900 text-sm"
                  >
                    + Add Facility
                  </button>
                </div>
              </div>

              <!-- Status -->
              <div>
                <label class="block text-sm font-medium text-gray-700">Status</label>
                <div class="mt-1">
                  <label class="inline-flex items-center">
                    <input
                      v-model="form.is_active"
                      type="checkbox"
                      class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                    <span class="ml-2 text-sm text-gray-600">Active</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end">
              <Link
                :href="route('training-centers.index')"
                class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 mr-4"
              >
                Cancel
              </Link>
              <button
                type="submit"
                :disabled="form.processing"
                class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50"
              >
                Update Center
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
  trainingCenter: Object,
});

const form = useForm({
  name: props.trainingCenter.name,
  description: props.trainingCenter.description,
  location: props.trainingCenter.location,
  address: props.trainingCenter.address,
  capacity: props.trainingCenter.capacity,
  facilities: props.trainingCenter.facilities || [''],
  is_active: props.trainingCenter.is_active,
});

function addFacility() {
  form.facilities.push('');
}

function removeFacility(index) {
  form.facilities.splice(index, 1);
}

function submit() {
  form.put(route('training-centers.update', props.trainingCenter.id));
}
</script>