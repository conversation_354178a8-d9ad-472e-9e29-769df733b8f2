<template>
  <AppLayout :title="trainingCenter.name">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ trainingCenter.name }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Center Details -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6 mb-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Basic Info -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Center Information</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-500">Location</p>
                  <p class="text-gray-900">{{ trainingCenter.location }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Address</p>
                  <p class="text-gray-900">{{ trainingCenter.address }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Capacity</p>
                  <p class="text-gray-900">{{ trainingCenter.capacity }} students</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Status</p>
                  <span 
                    :class="[
                      'px-2 py-1 text-xs font-medium rounded-full',
                      trainingCenter.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ trainingCenter.is_active ? 'Active' : 'Inactive' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Facilities -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Facilities</h3>
              <ul class="list-disc list-inside space-y-2">
                <li 
                  v-for="(facility, index) in trainingCenter.facilities" 
                  :key="index"
                  class="text-gray-900"
                >
                  {{ facility }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Actions -->
          <div class="mt-6 flex justify-end space-x-4">
            <Link
              :href="route('training-centers.edit', trainingCenter.id)"
              class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
            >
              Edit Center
            </Link>
            <button
              @click="confirmDelete"
              class="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
            >
              Delete Center
            </button>
          </div>
        </div>

        <!-- Active Sessions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-4">Active Sessions</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trainer</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="session in trainingCenter.training_sessions" :key="session.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Link
                      :href="route('training-sessions.show', session.id)"
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      {{ session.title }}
                    </Link>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {{ session.trainer.name }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {{ new Date(session.start_date).toLocaleDateString() }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        {
                          'bg-yellow-100 text-yellow-800': session.status === 'scheduled',
                          'bg-green-100 text-green-800': session.status === 'in_progress',
                          'bg-blue-100 text-blue-800': session.status === 'completed',
                          'bg-red-100 text-red-800': session.status === 'cancelled'
                        }
                      ]"
                    >
                      {{ session.status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {{ session.students_count }} / {{ session.max_participants }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
  trainingCenter: Object,
});

function confirmDelete() {
  if (confirm('Are you sure you want to delete this training center?')) {
    router.delete(route('training-centers.destroy', props.trainingCenter.id));
  }
}
</script>