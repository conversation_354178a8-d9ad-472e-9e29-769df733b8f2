<template>
  <AppLayout title="Training Centers">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Training Centers
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <!-- Header and Create Button -->
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-semibold">All Training Centers</h3>
            <Link 
              :href="route('training-centers.create')" 
              class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
            >
              Create New Center
            </Link>
          </div>

          <!-- Search and Filter -->
          <div class="mb-6">
            <input
              v-model="search"
              type="text"
              placeholder="Search centers..."
              class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>

          <!-- Training Centers Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Capacity</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active Sessions</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="center in filteredCenters" :key="center.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Link 
                      :href="route('training-centers.show', center.id)" 
                      class="text-indigo-600 hover:text-indigo-900 font-medium"
                    >
                      {{ center.name }}
                    </Link>
                    <p class="text-sm text-gray-500">{{ center.description }}</p>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ center.location }}</div>
                    <div class="text-sm text-gray-500">{{ center.address }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ center.capacity }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ center.training_sessions_count }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        center.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      ]"
                    >
                      {{ center.is_active ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link 
                      :href="route('training-centers.edit', center.id)"
                      class="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      Edit
                    </Link>
                    <button
                      @click="confirmDelete(center)"
                      class="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <Pagination :links="centers.links" class="mt-6" />
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';

const props = defineProps({
  centers: Object,
});

const search = ref('');

const filteredCenters = computed(() => {
  return props.centers.data.filter(center => {
    return center.name.toLowerCase().includes(search.value.toLowerCase()) ||
           center.location.toLowerCase().includes(search.value.toLowerCase());
  });
});

function confirmDelete(center) {
  if (confirm(`Are you sure you want to delete ${center.name}?`)) {
    router.delete(route('training-centers.destroy', center.id));
  }
}
</script>