<template>
  <AppLayout title="Welcome">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Welcome to TNET Training Tracker
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-4">Training Centers Map</h3>
          <TrainingCenterMap :trainingCenters="trainingCenters" />
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import TrainingCenterMap from '@/Components/TrainingCenterMap.vue';

const props = defineProps({
  trainingCenters: Array,
});
</script>