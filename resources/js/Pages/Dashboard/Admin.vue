<template>
  <AppLayout title="Admin Dashboard">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Admin Dashboard
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Training Centers</div>
            <div class="text-3xl font-bold">{{ centers_count }}</div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Active Trainers</div>
            <div class="text-3xl font-bold">{{ active_trainers }}</div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Total Students</div>
            <div class="text-3xl font-bold">{{ total_students }}</div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Upcoming Sessions</div>
            <div class="text-3xl font-bold">{{ upcoming_sessions }}</div>
          </div>
        </div>

        <!-- Recent Data Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Recent Centers -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">Recent Training Centers</h3>
              <Link :href="route('training-centers.index')" class="text-indigo-600 hover:text-indigo-900">
                View All
              </Link>
            </div>
            <div class="space-y-4">
              <div v-for="center in recent_centers" :key="center.id" class="border-b pb-4 last:border-b-0">
                <div class="flex justify-between items-start">
                  <div>
                    <Link :href="route('training-centers.show', center.id)" class="text-lg font-medium text-gray-900 hover:text-indigo-600">
                      {{ center.name }}
                    </Link>
                    <p class="text-sm text-gray-500">{{ center.location }}</p>
                  </div>
                  <span :class="[
                    'px-2 py-1 text-xs font-medium rounded-full',
                    center.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]">
                    {{ center.is_active ? 'Active' : 'Inactive' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Sessions -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">Recent Training Sessions</h3>
              <Link :href="route('training-sessions.index')" class="text-indigo-600 hover:text-indigo-900">
                View All
              </Link>
            </div>
            <div class="space-y-4">
              <div v-for="session in recent_sessions" :key="session.id" class="border-b pb-4 last:border-b-0">
                <div>
                  <Link :href="route('training-sessions.show', session.id)" class="text-lg font-medium text-gray-900 hover:text-indigo-600">
                    {{ session.title }}
                  </Link>
                  <p class="text-sm text-gray-500">
                    {{ session.training_center.name }} • {{ session.trainer.name }}
                  </p>
                  <div class="flex items-center mt-2">
                    <span :class="[
                      'px-2 py-1 text-xs font-medium rounded-full',
                      {
                        'bg-yellow-100 text-yellow-800': session.status === 'scheduled',
                        'bg-green-100 text-green-800': session.status === 'in_progress',
                        'bg-blue-100 text-blue-800': session.status === 'completed',
                        'bg-red-100 text-red-800': session.status === 'cancelled'
                      }
                    ]">
                      {{ session.status }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

defineProps({
  centers_count: Number,
  active_trainers: Number,
  total_students: Number,
  upcoming_sessions: Number,
  recent_centers: Array,
  recent_sessions: Array,
});
</script>
