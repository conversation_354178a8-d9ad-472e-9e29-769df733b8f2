<template>
  <AppLayout title="Trainer Dashboard">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Trainer Dashboard
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Active Sessions</div>
            <div class="text-3xl font-bold">{{ active_sessions.length }}</div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Upcoming Sessions</div>
            <div class="text-3xl font-bold">{{ upcoming_sessions.length }}</div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Total Students</div>
            <div class="text-3xl font-bold">{{ total_students }}</div>
          </div>
        </div>

        <!-- Active Sessions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6 mb-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Active Sessions</h3>
            <Link :href="route('training-sessions.index')" class="text-indigo-600 hover:text-indigo-900">
              View All Sessions
            </Link>
          </div>
          
          <div class="space-y-6">
            <div v-for="session in active_sessions" :key="session.id" class="border-b pb-6 last:border-b-0">
              <div class="flex justify-between items-start mb-4">
                <div>
                  <Link :href="route('training-sessions.show', session.id)" class="text-xl font-medium text-gray-900 hover:text-indigo-600">
                    {{ session.title }}
                  </Link>
                  <p class="text-sm text-gray-500">{{ session.training_center.name }}</p>
                </div>
                <div class="text-right">
                  <span class="bg-green-100 text-green-800 px-2 py-1 text-xs font-medium rounded-full">
                    In Progress
                  </span>
                  <p class="text-sm text-gray-500 mt-1">
                    {{ session.students.length }} / {{ session.max_participants }} Students
                  </p>
                </div>
              </div>

              <!-- Student List -->
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                      <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="student in session.students" :key="student.id">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ student.name }}</div>
                        <div class="text-sm text-gray-500">{{ student.email }}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span :class="[
                          'px-2 py-1 text-xs font-medium rounded-full',
                          {
                            'bg-yellow-100 text-yellow-800': student.pivot.status === 'enrolled',
                            'bg-green-100 text-green-800': student.pivot.status === 'in_progress',
                            'bg-blue-100 text-blue-800': student.pivot.status === 'completed',
                            'bg-red-100 text-red-800': student.pivot.status === 'dropped'
                          }
                        ]">
                          {{ student.pivot.status }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span :class="[
                          'px-2 py-1 text-xs font-medium rounded-full',
                          {
                            'bg-red-100 text-red-800': student.pivot.payment_status === 'pending',
                            'bg-yellow-100 text-yellow-800': student.pivot.payment_status === 'partial',
                            'bg-green-100 text-green-800': student.pivot.payment_status === 'completed'
                          }
                        ]">
                          {{ student.pivot.payment_status }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button 
                          @click="updateStudentStatus(session.id, student.id, 'completed')"
                          class="text-indigo-600 hover:text-indigo-900 ml-4"
                        >
                          Mark Complete
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Upcoming Sessions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-4">Upcoming Sessions</h3>
          <div class="space-y-4">
            <div v-for="session in upcoming_sessions" :key="session.id" class="border-b pb-4 last:border-b-0">
              <div class="flex justify-between items-start">
                <div>
                  <Link :href="route('training-sessions.show', session.id)" class="text-lg font-medium text-gray-900 hover:text-indigo-600">
                    {{ session.title }}
                  </Link>
                  <p class="text-sm text-gray-500">{{ session.training_center.name }}</p>
                  <p class="text-sm text-gray-500">
                    Starts: {{ new Date(session.start_date).toLocaleDateString() }}
                  </p>
                </div>
                <div class="text-right">
                  <span class="bg-yellow-100 text-yellow-800 px-2 py-1 text-xs font-medium rounded-full">
                    Scheduled
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

defineProps({
  active_sessions: Array,
  upcoming_sessions: Array,
  completed_sessions: Number,
  total_students: Number,
});

function updateStudentStatus(sessionId, studentId, status) {
  router.patch(route('enrollments.update-status', sessionId), {
    student_id: studentId,
    status: status,
  });
}
</script>
