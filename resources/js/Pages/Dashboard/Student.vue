<template>
  <AppLayout title="Student Dashboard">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Student Dashboard
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Enrolled Sessions</div>
            <div class="text-3xl font-bold">{{ enrolled_sessions.length }}</div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Completed Sessions</div>
            <div class="text-3xl font-bold">{{ completed_sessions }}</div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="text-gray-900 text-xl font-semibold mb-2">Pending Payments</div>
            <div class="text-3xl font-bold">{{ pending_payments }}</div>
          </div>
        </div>

        <!-- Enrolled Sessions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6 mb-6">
          <h3 class="text-lg font-semibold mb-4">My Enrolled Sessions</h3>
          <div class="space-y-6">
            <div v-for="session in enrolled_sessions" :key="session.id" class="border-b pb-6 last:border-b-0">
              <div class="flex justify-between items-start mb-2">
                <div>
                  <Link :href="route('training-sessions.show', session.id)" class="text-lg font-medium text-gray-900 hover:text-indigo-600">
                    {{ session.title }}
                  </Link>
                  <p class="text-sm text-gray-500">{{ session.training_center.name }}</p>
                  <p class="text-sm text-gray-500">Trainer: {{ session.trainer.name }}</p>
                </div>
                <div class="text-right">
                  <span :class="[
                    'px-2 py-1 text-xs font-medium rounded-full',
                    {
                      'bg-yellow-100 text-yellow-800': session.pivot.status === 'enrolled',
                      'bg-green-100 text-green-800': session.pivot.status === 'in_progress',
                      'bg-blue-100 text-blue-800': session.pivot.status === 'completed',
                      'bg-red-100 text-red-800': session.pivot.status === 'dropped'
                    }
                  ]">
                    {{ session.pivot.status }}
                  </span>
                  <p class="text-sm text-gray-500 mt-1">
                    Payment: {{ session.pivot.payment_status }}
                  </p>
                </div>
              </div>
              <div class="mt-4 flex justify-end">
                <button
                  v-if="session.pivot.status !== 'completed'"
                  @click="withdrawFromSession(session.id)"
                  class="text-red-600 hover:text-red-900 text-sm font-medium"
                >
                  Withdraw
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Available Sessions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-4">Available Sessions</h3>
          <div class="space-y-6">
            <div v-for="session in upcoming_sessions" :key="session.id" class="border-b pb-6 last:border-b-0">
              <div class="flex justify-between items-start mb-2">
                <div>
                  <Link :href="route('training-sessions.show', session.id)" class="text-lg font-medium text-gray-900 hover:text-indigo-600">
                    {{ session.title }}
                  </Link>
                  <p class="text-sm text-gray-500">{{ session.training_center.name }}</p>
                  <p class="text-sm text-gray-500">Trainer: {{ session.trainer.name }}</p>
                  <p class="text-sm text-gray-500">
                    Starts: {{ new Date(session.start_date).toLocaleDateString() }}
                  </p>
                </div>
                <div class="text-right">
                  <span class="bg-green-100 text-green-800 px-2 py-1 text-xs font-medium rounded-full">
                    Open for Enrollment
                  </span>
                  <p class="text-sm text-gray-500 mt-1">
                    Fee: ${{ session.fee }}
                  </p>
                </div>
              </div>
              <div class="mt-4 flex justify-end">
                <button
                  @click="enrollInSession(session.id)"
                  class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
                >
                  Enroll Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

defineProps({
  enrolled_sessions: Array,
  completed_sessions: Number,
  pending_payments: Number,
  upcoming_sessions: Array,
});

function enrollInSession(sessionId) {
  router.post(route('enrollments.store', sessionId), {
    payment_amount: 0, // This should be replaced with a proper payment form
  });
}

function withdrawFromSession(sessionId) {
  if (confirm('Are you sure you want to withdraw from this session?')) {
    router.delete(route('enrollments.destroy', sessionId));
  }
}
</script>
