<template>
  <AppLayout title="Training Sessions">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Training Sessions
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <!-- Header and Create Button -->
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-semibold">All Training Sessions</h3>
            <Link 
              :href="route('training-sessions.create')" 
              class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
            >
              Create New Session
            </Link>
          </div>

          <!-- Search and Filters -->
          <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <input
              v-model="search"
              type="text"
              placeholder="Search sessions..."
              class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <select
              v-model="statusFilter"
              class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">All Statuses</option>
              <option value="scheduled">Scheduled</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <select
              v-model="centerFilter"
              class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">All Centers</option>
              <option 
                v-for="center in trainingCenters" 
                :key="center.id" 
                :value="center.id"
              >
                {{ center.name }}
              </option>
            </select>
          </div>

          <!-- Sessions Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Center</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trainer</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dates</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Participants</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="session in filteredSessions" :key="session.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Link 
                      :href="route('training-sessions.show', session.id)" 
                      class="text-indigo-600 hover:text-indigo-900 font-medium"
                    >
                      {{ session.title }}
                    </Link>
                    <p class="text-sm text-gray-500">{{ session.description }}</p>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Link
                      :href="route('training-centers.show', session.training_center.id)"
                      class="text-gray-900 hover:text-indigo-600"
                    >
                      {{ session.training_center.name }}
                    </Link>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {{ session.trainer.name }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {{ new Date(session.start_date).toLocaleDateString() }}
                    </div>
                    <div class="text-sm text-gray-500">
                      to {{ new Date(session.end_date).toLocaleDateString() }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        {
                          'bg-yellow-100 text-yellow-800': session.status === 'scheduled',
                          'bg-green-100 text-green-800': session.status === 'in_progress',
                          'bg-blue-100 text-blue-800': session.status === 'completed',
                          'bg-red-100 text-red-800': session.status === 'cancelled'
                        }
                      ]"
                    >
                      {{ session.status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {{ session.students_count }} / {{ session.max_participants }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link 
                      :href="route('training-sessions.edit', session.id)"
                      class="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      Edit
                    </Link>
                    <button
                      @click="confirmDelete(session)"
                      class="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <Pagination :links="sessions.links" class="mt-6" />
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';

const props = defineProps({
  sessions: Object,
  trainingCenters: Array,
});

const search = ref('');
const statusFilter = ref('');
const centerFilter = ref('');

const filteredSessions = computed(() => {
  return props.sessions.data.filter(session => {
    const matchesSearch = session.title.toLowerCase().includes(search.value.toLowerCase()) ||
                         session.description.toLowerCase().includes(search.value.toLowerCase());
    
    const matchesStatus = statusFilter.value ? session.status === statusFilter.value : true;
    
    const matchesCenter = centerFilter.value ? session.training_center.id === centerFilter.value : true;
    
    return matchesSearch && matchesStatus && matchesCenter;
  });
});

function confirmDelete(session) {
  if (confirm(`Are you sure you want to delete ${session.title}?`)) {
    router.delete(route('training-sessions.destroy', session.id));
  }
}
</script>