<template>
  <AppLayout :title="trainingSession.title">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ trainingSession.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Session Details -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6 mb-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Basic Info -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Session Information</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-500">Training Center</p>
                  <Link
                    :href="route('training-centers.show', trainingSession.training_center.id)"
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    {{ trainingSession.training_center.name }}
                  </Link>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Trainer</p>
                  <p class="text-gray-900">{{ trainingSession.trainer.name }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Dates</p>
                  <p class="text-gray-900">
                    {{ new Date(trainingSession.start_date).toLocaleString() }} - 
                    {{ new Date(trainingSession.end_date).toLocaleString() }}
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Status</p>
                  <span 
                    :class="[
                      'px-2 py-1 text-xs font-medium rounded-full',
                      {
                        'bg-yellow-100 text-yellow-800': trainingSession.status === 'scheduled',
                        'bg-green-100 text-green-800': trainingSession.status === 'in_progress',
                        'bg-blue-100 text-blue-800': trainingSession.status === 'completed',
                        'bg-red-100 text-red-800': trainingSession.status === 'cancelled'
                      }
                    ]"
                  >
                    {{ trainingSession.status }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Capacity and Fee -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Session Details</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-500">Participants</p>
                  <p class="text-gray-900">
                    {{ trainingSession.students_count }} / {{ trainingSession.max_participants }}
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Fee</p>
                  <p class="text-gray-900">${{ trainingSession.fee }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Materials</p>
                  <ul class="list-disc list-inside">
                    <li 
                      v-for="(material, index) in trainingSession.materials" 
                      :key="index"
                      class="text-gray-900"
                    >
                      {{ material }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Description -->
          <div class="mt-6">
            <h3 class="text-lg font-semibold mb-2">Description</h3>
            <p class="text-gray-900 whitespace-pre-line">{{ trainingSession.description }}</p>
          </div>

          <!-- Actions -->
          <div class="mt-6 flex justify-end space-x-4">
            <Link
              :href="route('training-sessions.edit', trainingSession.id)"
              class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
            >
              Edit Session
            </Link>
            <button
              @click="confirmDelete"
              class="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
            >
              Delete Session
            </button>
          </div>
        </div>

        <!-- Enrolled Students -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-4">Enrolled Students</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="student in trainingSession.students" :key="student.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ student.name }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ student.email }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        {
                          'bg-yellow-100 text-yellow-800': student.pivot.status === 'enrolled',
                          'bg-green-100 text-green-800': student.pivot.status === 'in_progress',
                          'bg-blue-100 text-blue-800': student.pivot.status === 'completed',
                          'bg-red-100 text-red-800': student.pivot.status === 'dropped'
                        }
                      ]"
                    >
                      {{ student.pivot.status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        {
                          'bg-red-100 text-red-800': student.pivot.payment_status === 'pending',
                          'bg-yellow-100 text-yellow-800': student.pivot.payment_status === 'partial',
                          'bg-green-100 text-green-800': student.pivot.payment_status === 'completed'
                        }
                      ]"
                    >
                      {{ student.pivot.payment_status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      v-if="student.pivot.status !== 'completed'"
                      @click="updateStudentStatus(student.id, 'completed')"
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      Mark Complete
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
  trainingSession: Object,
});

function confirmDelete() {
  if (confirm('Are you sure you want to delete this training session?')) {
    router.delete(route('training-sessions.destroy', props.trainingSession.id));
  }
}

function updateStudentStatus(studentId, status) {
  router.patch(route('enrollments.update-status', props.trainingSession.id), {
    student_id: studentId,
    status: status,
  });
}
</script>