<template>
  <AppLayout title="Create Training Session">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Create New Training Session
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <form @submit.prevent="submit">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Title -->
              <div>
                <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
                <input
                  v-model="form.title"
                  type="text"
                  id="title"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.title" class="text-sm text-red-600 mt-1">
                  {{ form.errors.title }}
                </p>
              </div>

              <!-- Training Center -->
              <div>
                <label for="training_center_id" class="block text-sm font-medium text-gray-700">Training Center</label>
                <select
                  v-model="form.training_center_id"
                  id="training_center_id"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                >
                  <option value="">Select a center</option>
                  <option 
                    v-for="center in trainingCenters" 
                    :key="center.id" 
                    :value="center.id"
                  >
                    {{ center.name }}
                  </option>
                </select>
                <p v-if="form.errors.training_center_id" class="text-sm text-red-600 mt-1">
                  {{ form.errors.training_center_id }}
                </p>
              </div>

              <!-- Trainer -->
              <div>
                <label for="trainer_id" class="block text-sm font-medium text-gray-700">Trainer</label>
                <select
                  v-model="form.trainer_id"
                  id="trainer_id"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                >
                  <option value="">Select a trainer</option>
                  <option 
                    v-for="trainer in trainers" 
                    :key="trainer.id" 
                    :value="trainer.id"
                  >
                    {{ trainer.name }}
                  </option>
                </select>
                <p v-if="form.errors.trainer_id" class="text-sm text-red-600 mt-1">
                  {{ form.errors.trainer_id }}
                </p>
              </div>

              <!-- Dates -->
              <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                <input
                  v-model="form.start_date"
                  type="datetime-local"
                  id="start_date"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.start_date" class="text-sm text-red-600 mt-1">
                  {{ form.errors.start_date }}
                </p>
              </div>

              <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                <input
                  v-model="form.end_date"
                  type="datetime-local"
                  id="end_date"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.end_date" class="text-sm text-red-600 mt-1">
                  {{ form.errors.end_date }}
                </p>
              </div>

              <!-- Capacity and Fee -->
              <div>
                <label for="max_participants" class="block text-sm font-medium text-gray-700">Max Participants</label>
                <input
                  v-model="form.max_participants"
                  type="number"
                  id="max_participants"
                  min="1"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.max_participants" class="text-sm text-red-600 mt-1">
                  {{ form.errors.max_participants }}
                </p>
              </div>

              <div>
                <label for="fee" class="block text-sm font-medium text-gray-700">Fee</label>
                <input
                  v-model="form.fee"
                  type="number"
                  id="fee"
                  min="0"
                  step="0.01"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.fee" class="text-sm text-red-600 mt-1">
                  {{ form.errors.fee }}
                </p>
              </div>

              <!-- Materials -->
              <div class="col-span-full">
                <label class="block text-sm font-medium text-gray-700">Materials</label>
                <div class="mt-1 space-y-2">
                  <div v-for="(material, index) in form.materials" :key="index" class="flex items-center">
                    <input
                      v-model="form.materials[index]"
                      type="text"
                      class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                    <button
                      type="button"
                      @click="removeMaterial(index)"
                      class="ml-2 text-red-600 hover:text-red-900"
                    >
                      Remove
                    </button>
                  </div>
                  <button
                    type="button"
                    @click="addMaterial"
                    class="text-indigo-600 hover:text-indigo-900 text-sm"
                  >
                    + Add Material
                  </button>
                </div>
              </div>

              <!-- Description -->
              <div class="col-span-full">
                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                <textarea
                  v-model="form.description"
                  id="description"
                  rows="4"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                ></textarea>
                <p v-if="form.errors.description" class="text-sm text-red-600 mt-1">
                  {{ form.errors.description }}
                </p>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end">
              <Link
                :href="route('training-sessions.index')"
                class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 mr-4"
              >
                Cancel
              </Link>
              <button
                type="submit"
                :disabled="form.processing"
                class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50"
              >
                Create Session
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
  trainingCenters: Array,
  trainers: Array,
});

const form = useForm({
  title: '',
  description: '',
  training_center_id: '',
  trainer_id: '',
  start_date: '',
  end_date: '',
  max_participants: 10,
  fee: 0,
  materials: [''],
});

function addMaterial() {
  form.materials.push('');
}

function removeMaterial(index) {
  form.materials.splice(index, 1);
}

function submit() {
  form.post(route('training-sessions.store'), {
    onSuccess: () => form.reset(),
  });
}
</script>