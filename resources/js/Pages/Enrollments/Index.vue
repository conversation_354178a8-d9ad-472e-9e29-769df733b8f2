<template>
  <AppLayout title="Enrollments">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Enrollments
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <!-- Header and Filters -->
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-semibold">All Enrollments</h3>
            <div class="flex space-x-4">
              <select
                v-model="statusFilter"
                class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="">All Statuses</option>
                <option value="enrolled">Enrolled</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="dropped">Dropped</option>
              </select>
              <select
                v-model="paymentFilter"
                class="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="">All Payment Statuses</option>
                <option value="pending">Pending</option>
                <option value="partial">Partial</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>

          <!-- Enrollments Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enrolled At</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="enrollment in filteredEnrollments" :key="enrollment.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      {{ enrollment.student.name }}
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ enrollment.student.email }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Link
                      :href="route('training-sessions.show', enrollment.training_session.id)"
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      {{ enrollment.training_session.title }}
                    </Link>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        {
                          'bg-yellow-100 text-yellow-800': enrollment.status === 'enrolled',
                          'bg-green-100 text-green-800': enrollment.status === 'in_progress',
                          'bg-blue-100 text-blue-800': enrollment.status === 'completed',
                          'bg-red-100 text-red-800': enrollment.status === 'dropped'
                        }
                      ]"
                    >
                      {{ enrollment.status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        {
                          'bg-red-100 text-red-800': enrollment.payment_status === 'pending',
                          'bg-yellow-100 text-yellow-800': enrollment.payment_status === 'partial',
                          'bg-green-100 text-green-800': enrollment.payment_status === 'completed'
                        }
                      ]"
                    >
                      {{ enrollment.payment_status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {{ new Date(enrollment.created_at).toLocaleDateString() }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      :href="route('enrollments.edit', enrollment.id)"
                      class="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      Edit
                    </Link>
                    <button
                      @click="confirmDelete(enrollment)"
                      class="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <Pagination :links="enrollments.links" class="mt-6" />
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';

const props = defineProps({
  enrollments: Object,
});

const statusFilter = ref('');
const paymentFilter = ref('');

const filteredEnrollments = computed(() => {
  return props.enrollments.data.filter(enrollment => {
    const matchesStatus = statusFilter.value ? enrollment.status === statusFilter.value : true;
    const matchesPayment = paymentFilter.value ? enrollment.payment_status === paymentFilter.value : true;
    return matchesStatus && matchesPayment;
  });
});

function confirmDelete(enrollment) {
  if (confirm(`Are you sure you want to delete ${enrollment.student.name}'s enrollment?`)) {
    router.delete(route('enrollments.destroy', enrollment.id));
  }
}
</script>