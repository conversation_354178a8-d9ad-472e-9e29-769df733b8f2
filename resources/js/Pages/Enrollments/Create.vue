<template>
  <AppLayout title="Create Enrollment">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Create New Enrollment
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <form @submit.prevent="submit">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Student -->
              <div>
                <label for="student_id" class="block text-sm font-medium text-gray-700">Student</label>
                <select
                  v-model="form.student_id"
                  id="student_id"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                >
                  <option value="">Select a student</option>
                  <option 
                    v-for="student in students" 
                    :key="student.id" 
                    :value="student.id"
                  >
                    {{ student.name }} ({{ student.email }})
                  </option>
                </select>
                <p v-if="form.errors.student_id" class="text-sm text-red-600 mt-1">
                  {{ form.errors.student_id }}
                </p>
              </div>

              <!-- Training Session -->
              <div>
                <label for="training_session_id" class="block text-sm font-medium text-gray-700">Training Session</label>
                <select
                  v-model="form.training_session_id"
                  id="training_session_id"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                >
                  <option value="">Select a session</option>
                  <option 
                    v-for="session in trainingSessions" 
                    :key="session.id" 
                    :value="session.id"
                  >
                    {{ session.title }} ({{ session.training_center.name }})
                  </option>
                </select>
                <p v-if="form.errors.training_session_id" class="text-sm text-red-600 mt-1">
                  {{ form.errors.training_session_id }}
                </p>
              </div>

              <!-- Status -->
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                <select
                  v-model="form.status"
                  id="status"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                >
                  <option value="enrolled">Enrolled</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="dropped">Dropped</option>
                </select>
                <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                  {{ form.errors.status }}
                </p>
              </div>

              <!-- Payment Status -->
              <div>
                <label for="payment_status" class="block text-sm font-medium text-gray-700">Payment Status</label>
                <select
                  v-model="form.payment_status"
                  id="payment_status"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                >
                  <option value="pending">Pending</option>
                  <option value="partial">Partial</option>
                  <option value="completed">Completed</option>
                </select>
                <p v-if="form.errors.payment_status" class="text-sm text-red-600 mt-1">
                  {{ form.errors.payment_status }}
                </p>
              </div>

              <!-- Payment Amount -->
              <div>
                <label for="payment_amount" class="block text-sm font-medium text-gray-700">Payment Amount</label>
                <input
                  v-model="form.payment_amount"
                  type="number"
                  id="payment_amount"
                  min="0"
                  step="0.01"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                />
                <p v-if="form.errors.payment_amount" class="text-sm text-red-600 mt-1">
                  {{ form.errors.payment_amount }}
                </p>
              </div>

              <!-- Notes -->
              <div class="col-span-full">
                <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                <textarea
                  v-model="form.notes"
                  id="notes"
                  rows="3"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                ></textarea>
                <p v-if="form.errors.notes" class="text-sm text-red-600 mt-1">
                  {{ form.errors.notes }}
                </p>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end">
              <Link
                :href="route('enrollments.index')"
                class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 mr-4"
              >
                Cancel
              </Link>
              <button
                type="submit"
                :disabled="form.processing"
                class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50"
              >
                Create Enrollment
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
  students: Array,
  trainingSessions: Array,
});

const form = useForm({
  student_id: '',
  training_session_id: '',
  status: 'enrolled',
  payment_status: 'pending',
  payment_amount: 0,
  notes: '',
});

function submit() {
  form.post(route('enrollments.store'), {
    onSuccess: () => form.reset(),
  });
}
</script>