<template>
  <AppLayout :title="`Enrollment Details`">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Enrollment Details
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
          <!-- Enrollment Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Student Information -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Student Information</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-500">Name</p>
                  <p class="text-gray-900">{{ enrollment.student.name }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Email</p>
                  <p class="text-gray-900">{{ enrollment.student.email }}</p>
                </div>
              </div>
            </div>

            <!-- Training Session Information -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Training Session</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-500">Title</p>
                  <Link
                    :href="route('training-sessions.show', enrollment.training_session.id)"
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    {{ enrollment.training_session.title }}
                  </Link>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Center</p>
                  <Link
                    :href="route('training-centers.show', enrollment.training_session.training_center.id)"
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    {{ enrollment.training_session.training_center.name }}
                  </Link>
                </div>
              </div>
            </div>

            <!-- Enrollment Status -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Enrollment Status</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-500">Status</p>
                  <span 
                    :class="[
                      'px-2 py-1 text-xs font-medium rounded-full',
                      {
                        'bg-yellow-100 text-yellow-800': enrollment.status === 'enrolled',
                        'bg-green-100 text-green-800': enrollment.status === 'in_progress',
                        'bg-blue-100 text-blue-800': enrollment.status === 'completed',
                        'bg-red-100 text-red-800': enrollment.status === 'dropped'
                      }
                    ]"
                  >
                    {{ enrollment.status }}
                  </span>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Enrolled At</p>
                  <p class="text-gray-900">
                    {{ new Date(enrollment.created_at).toLocaleString() }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Payment Information -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Payment Information</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-500">Status</p>
                  <span 
                    :class="[
                      'px-2 py-1 text-xs font-medium rounded-full',
                      {
                        'bg-red-100 text-red-800': enrollment.payment_status === 'pending',
                        'bg-yellow-100 text-yellow-800': enrollment.payment_status === 'partial',
                        'bg-green-100 text-green-800': enrollment.payment_status === 'completed'
                      }
                    ]"
                  >
                    {{ enrollment.payment_status }}
                  </span>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Amount</p>
                  <p class="text-gray-900">${{ enrollment.payment_amount }}</p>
                </div>
              </div>
            </div>

            <!-- Notes -->
            <div class="col-span-full">
              <h3 class="text-lg font-semibold mb-2">Notes</h3>
              <p class="text-gray-900 whitespace-pre-line">{{ enrollment.notes }}</p>
            </div>
          </div>

          <!-- Actions -->
          <div class="mt-6 flex justify-end space-x-4">
            <Link
              :href="route('enrollments.edit', enrollment.id)"
              class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
            >
              Edit Enrollment
            </Link>
            <button
              @click="confirmDelete"
              class="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
            >
              Delete Enrollment
            </button>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
  enrollment: Object,
});

function confirmDelete() {
  if (confirm('Are you sure you want to delete this enrollment?')) {
    router.delete(route('enrollments.destroy', props.enrollment.id));
  }
}
</script>