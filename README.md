## About TNET Training Tracker

## Prompt: TNET Training Tracker
- Here’s a structured system prompt for the **Training Management & Tracking System (TMS)**, including user roles, permissions, and dashboards with checkboxes:

---

**System Prompt for Training Management & Tracking System (TMS)**  

The **Training Management & Tracking System (TMS)** is designed to manage training centers, sessions, trainers, students, payments, attendance, and performance tracking. It includes role-based access, dashboards, notifications, and payment tracking.

## **User Roles & Permissions**  
Each user role has specific permissions, outlined below:

✅ **Admin**  
- [ ] Manage users (add/edit/delete)  
- [ ] Approve trainers after module completion  
- [ ] Manage training centers & mapping  
- [ ] View national coverage & reports  
- [ ] Monitor payments & generate financial reports  
- [ ] Configure system settings  

✅ **Trainer**  
- [ ] Conduct training sessions  
- [ ] Track and mark attendance  
- [ ] Upload training manuals  
- [ ] Assign grades per module  
- [ ] View student progress reports  

✅ **Student**  
- [ ] Enroll in programs (Certificate, Diploma, Master's)  
- [ ] Attend and track session progress  
- [ ] View grades & learning progress  
- [ ] Apply to become a trainer after completing 5 modules  
- [ ] Download training materials  

✅ **Finance Team**  
- [ ] Track payments per module  
- [ ] Generate financial statements  
- [ ] Send payment reminders  

## **Dashboards**  

✅ **Admin Dashboard**  
- [ ] National training center map  
- [ ] Student enrollment statistics  
- [ ] Trainer approvals & applications  
- [ ] Payment & financial overview  

✅ **Trainer Dashboard**  
- [ ] Assigned training centers  
- [ ] Upcoming training sessions  
- [ ] Student attendance & grading panel  
- [ ] Shared training materials  

✅ **Student Dashboard**  
- [ ] Personal learning progress tracker  
- [ ] Attendance records  
- [ ] Payment status & pending fees  
- [ ] Eligibility for trainer application  

✅ **Finance Dashboard**  
- [ ] Fees collected & pending payments  
- [ ] Monthly and annual revenue breakdown  
- [ ] Payment gateway integration reports  

## **System Notifications & Alerts**  

✅ **For Admins**  
- [ ] Trainer application approvals  
- [ ] Payment collection alerts  
- [ ] System usage reports  

✅ **For Trainers**  
- [ ] Upcoming session reminders  
- [ ] Attendance alerts  
- [ ] Student progress reports  

✅ **For Students**  
- [ ] Session reminders  
- [ ] Payment due notifications  
- [ ] Missed session alerts  


## Functional Requirements

### 2.1 User Management
- Role-based access control (Admin, Trainer, Student, Finance Team)
- Student profile creation and management
- Trainer profile with session and grading access
- Trainer approval process after 5th module completion

### 2.2 Training Center & Mapping
- Track and map training centers (village, region, national level)
- Assign trainers and students to centers
- Display national coverage map

### 2.3 Session & Attendance Management
- Schedule and track training sessions
- Record attendance for each session
- Automated session reminders for students and trainers
- Generate dropout rate reports
- Mark missed sessions for students

### 2.4 Learning Materials & Training Manuals
- Trainers can upload and share manuals
- Students can download session-based learning materials
- Materials categorized per module/program

### 2.5 Trainer Application & Approval Process
- Students eligible after completing five modules
- Apply with the required details
- Admin approval process
- Approved trainers can start their centers

### 2.6 Payment & Fee Tracking
- Track payments per module
- Generate financial reports
- Notify students of pending payments

### 2.7 Grading & Student Progress Tracking
- Trainers assign grades per module
- Students view grades & progress summary
- Auto-generated grade reports

### 2.8 Program & Course Management
- Track programs (Certificate, Diploma, Master's)
- Define modules for each program
- Allow students to enroll in different programs

### 2.9 Dashboards & Reports
- Admin Dashboard: National coverage, student progress, dropout rate, payments
- Trainer Dashboard: Assigned centers, upcoming sessions, grading, attendance
- Student Dashboard: Progress tracker, attendance, payments, grades
- Finance Dashboard: Fees collected, pending payments, financial reports

### 2.10 Notifications & Alerts
- Session reminders (SMS, Email, Push notifications)
- Payment due alerts
- Missed session notifications
- Trainer application eligibility alert
- Module completion notifications


